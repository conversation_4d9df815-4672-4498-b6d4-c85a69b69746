const Untils = require('./untils')
const request = require('sync-request'); //默认同步请求
const pbottleRPA = require('./pbottleRPA')
const xlsx = require("node-xlsx");
const fs = require('fs')
const php = require("php2javascript");
const {
	FormData
} = require('sync-request');
const ExcelJS = require('exceljs');
const path = require('path');
const global_access_token = 'Basic c2FiZXI6c2FiZXJfc2VjcmV0';
const untils = new Untils(global_access_token);

main()
const globalMsg = ''
async function main() {
	let codeerr = pbottleRPA.browserCMD_text(
		`body > div.el-message.el-message--warning.el-icon-warning-color > p`)
	console.log('codeerr', codeerr)
}

// async function main() {
// 	console.log('开始运行')
// 	pbottleRPA.browserCMD_click('body > section > section > section > main > div.g-layout-main__content > div > div.hide-side-layout__container-content > div > div.hide-side-layout__page-content > div > div.search_header > form > div > div > div:nth-child(2) > div > div.t-form__controls.t-is-success > div > div > div > div > div > input')
// 	pbottleRPA.sleep(2000)
// 	pbottleRPA.browserCMD_click('body > div:nth-child(8) > div > div > div > div > div > div.t-date-picker__table > table > tbody > tr:nth-child(2) > td.t-date-picker__cell.t-date-picker__cell--now')
// }

// 新用户弹窗确认
// async function main() {
// 	// let varifyImage = pbottleRPA.browserCMD_attr('div#slideVerify img', 'src')
// 	// console.log('varifyImage',varifyImage)
//     const newUser = untils.existImage2('/input/1920/newUser.png')
//     console.log('newUser',newUser)
// 	if(newUser){
// 		pbottleRPA.browserCMD_click('body > div:nth-child(13) > div > div.el-dialog__body > div > div > label > span > span')
// 		pbottleRPA.browserCMD_click('body > div:nth-child(13) > div > div.el-dialog__footer > div > button')
// 	}	
// }
